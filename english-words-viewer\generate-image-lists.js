#!/usr/bin/env node

/**
 * 自动生成图片列表JSON文件
 * 扫描 public/images/easy/ 和 public/images/hard/ 文件夹
 * 生成对应的 easy-images.json 和 hard-images.json 文件
 */

import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

// 支持的图片格式
const imageExtensions = ['.png', '.PNG', '.jpg', '.JPG', '.jpeg', '.JPEG', '.gif', '.GIF', '.webp', '.WEBP']

// 检查文件是否为图片
function isImageFile(filename) {
  const ext = path.extname(filename)
  return imageExtensions.includes(ext)
}

// 自然排序函数（正确处理数字）
function naturalSort(a, b) {
  return a.localeCompare(b, undefined, { numeric: true, sensitivity: 'base' })
}

// 扫描文件夹并生成图片列表
function scanImagesFolder(folderPath) {
  try {
    if (!fs.existsSync(folderPath)) {
      console.log(`文件夹不存在: ${folderPath}`)
      return []
    }

    const files = fs.readdirSync(folderPath)
    const imageFiles = files
      .filter(isImageFile)
      .sort(naturalSort)

    return imageFiles
  } catch (error) {
    console.error(`扫描文件夹失败: ${folderPath}`, error)
    return []
  }
}

// 生成JSON文件
function generateImageList(difficulty) {
  const imagesPath = path.join(__dirname, 'public', 'images', difficulty)
  const outputPath = path.join(__dirname, 'public', `${difficulty}-images.json`)

  console.log(`扫描 ${difficulty} 图片文件夹: ${imagesPath}`)
  
  const imageFiles = scanImagesFolder(imagesPath)
  
  if (imageFiles.length === 0) {
    console.log(`⚠️  ${difficulty} 文件夹中没有找到图片文件`)
    return
  }

  // 写入JSON文件
  const jsonContent = JSON.stringify(imageFiles, null, 2)
  fs.writeFileSync(outputPath, jsonContent, 'utf8')
  
  console.log(`✅ 生成 ${difficulty}-images.json: ${imageFiles.length} 张图片`)
  console.log(`   文件路径: ${outputPath}`)
  
  // 显示前几个文件名作为示例
  const preview = imageFiles.slice(0, 5)
  console.log(`   示例文件: ${preview.join(', ')}${imageFiles.length > 5 ? '...' : ''}`)
}

// 主函数
function main() {
  console.log('🔍 开始扫描图片文件夹...\n')
  
  // 检查public文件夹是否存在
  const publicPath = path.join(__dirname, 'public')
  if (!fs.existsSync(publicPath)) {
    console.error('❌ public 文件夹不存在！')
    process.exit(1)
  }

  // 生成简单单词图片列表
  generateImageList('easy')
  console.log()
  
  // 生成困难单词图片列表
  generateImageList('hard')
  console.log()
  
  console.log('🎉 图片列表生成完成！')
  console.log('\n📝 使用说明:')
  console.log('1. 将图片文件放入 public/images/easy/ 或 public/images/hard/ 文件夹')
  console.log('2. 运行 node generate-image-lists.js 重新生成列表')
  console.log('3. 刷新浏览器即可看到新的图片')
}

// 如果直接运行此脚本
if (process.argv[1] && process.argv[1].endsWith('generate-image-lists.js')) {
  main()
}

export { scanImagesFolder, generateImageList }
