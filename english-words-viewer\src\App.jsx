import { useState, useEffect } from 'react'
import './App.css'

function App() {
  const [currentView, setCurrentView] = useState('home') // 'home', 'easy', 'hard'
  const [currentImageIndex, setCurrentImageIndex] = useState(0)
  const [images, setImages] = useState([])
  const [loading, setLoading] = useState(false)

  // 动态发现图片文件（批量并发检测）
  const discoverImages = async (difficulty) => {
    const discoveredImages = []

    // 生成所有可能的文件名
    const generatePossibleFilenames = () => {
      const filenames = []

      // 基于你的命名模式生成可能的文件名
      // 01.png, 02.png ... 99.png
      for (let i = 1; i <= 99; i++) {
        filenames.push(`${i.toString().padStart(2, '0')}.png`)
        filenames.push(`${i.toString().padStart(2, '0')}.PNG`)
      }

      // 010.png, 011.png ... 0199.png
      for (let i = 10; i <= 199; i++) {
        filenames.push(`0${i}.png`)
        filenames.push(`0${i}.PNG`)
      }

      // 0100.png, 0101.png ... 0199.png
      for (let i = 100; i <= 199; i++) {
        filenames.push(`0${i}.png`)
        filenames.push(`0${i}.PNG`)
      }

      return [...new Set(filenames)] // 去重
    }

    const possibleFilenames = generatePossibleFilenames()

    // 批量并发检测文件存在性（限制并发数避免过载）
    const batchSize = 10
    for (let i = 0; i < possibleFilenames.length; i += batchSize) {
      const batch = possibleFilenames.slice(i, i + batchSize)
      const promises = batch.map(async (filename) => {
        try {
          const response = await fetch(`/images/${difficulty}/${filename}`, {
            method: 'HEAD',
            cache: 'no-cache'
          })
          return response.ok ? filename : null
        } catch (error) {
          return null
        }
      })

      const results = await Promise.all(promises)
      const validFiles = results.filter(filename => filename !== null)
      discoveredImages.push(...validFiles)

      // 如果连续多个批次都没有找到文件，可能已经到达末尾
      if (validFiles.length === 0 && discoveredImages.length > 0) {
        break
      }
    }

    return discoveredImages.sort()
  }

  // 加载图片列表（优先使用JSON，回退到动态发现）
  const loadImages = async (difficulty) => {
    setLoading(true)
    try {
      let imageList = []
      let loadMethod = 'unknown'

      // 首先尝试加载JSON文件
      try {
        const response = await fetch(`/${difficulty}-images.json?t=${Date.now()}`)
        if (response.ok) {
          imageList = await response.json()
          loadMethod = 'json'
          console.log(`📄 从JSON文件加载图片列表: ${imageList.length} 张图片`)
        }
      } catch (jsonError) {
        console.log('📄 JSON文件加载失败，尝试动态发现模式')
      }

      // 如果JSON文件为空或不存在，使用动态发现
      if (imageList.length === 0) {
        console.log('🔍 开始动态发现图片文件...')
        imageList = await discoverImages(difficulty)
        loadMethod = 'dynamic'

        if (imageList.length > 0) {
          console.log(`🎯 动态发现成功: ${imageList.length} 张图片`)
          // 可选：将发现的图片列表保存到localStorage作为缓存
          localStorage.setItem(`${difficulty}-images-cache`, JSON.stringify({
            timestamp: Date.now(),
            images: imageList
          }))
        }
      }

      if (imageList.length === 0) {
        throw new Error(`未找到任何图片文件。请确保图片文件存在于 public/images/${difficulty}/ 文件夹中`)
      }

      setImages(imageList)
      setCurrentImageIndex(0)
      setCurrentView(difficulty)

      // 显示加载信息
      const difficultyText = difficulty === 'easy' ? '简单' : '困难'
      const methodText = loadMethod === 'json' ? 'JSON文件' : '动态发现'
      console.log(`✅ 成功加载 ${imageList.length} 张${difficultyText}图片 (${methodText})`)

    } catch (error) {
      console.error('❌ 加载图片列表失败:', error)
      alert(`加载图片失败：${error.message}\n\n💡 解决方案：\n1. 确保图片文件存在于对应文件夹中\n2. 运行 npm run generate-images 重新生成图片列表\n3. 刷新页面重试`)
    }
    setLoading(false)
  }

  // 键盘导航
  useEffect(() => {
    const handleKeyPress = (e) => {
      if (currentView === 'easy' || currentView === 'hard') {
        if (e.key === 'ArrowLeft' || e.key === 'ArrowUp') {
          goToPrevious()
        } else if (e.key === 'ArrowRight' || e.key === 'ArrowDown') {
          goToNext()
        } else if (e.key === 'Escape') {
          goHome()
        }
      }
    }

    window.addEventListener('keydown', handleKeyPress)
    return () => window.removeEventListener('keydown', handleKeyPress)
  }, [currentView, currentImageIndex, images.length])

  const goToNext = () => {
    if (currentImageIndex < images.length - 1) {
      setCurrentImageIndex(currentImageIndex + 1)
    }
  }

  const goToPrevious = () => {
    if (currentImageIndex > 0) {
      setCurrentImageIndex(currentImageIndex - 1)
    }
  }

  const goHome = () => {
    setCurrentView('home')
    setImages([])
    setCurrentImageIndex(0)
  }

  // 触摸手势支持
  const [touchStart, setTouchStart] = useState(null)
  const [touchEnd, setTouchEnd] = useState(null)

  const minSwipeDistance = 50

  const onTouchStart = (e) => {
    setTouchEnd(null)
    setTouchStart(e.targetTouches[0].clientX)
  }

  const onTouchMove = (e) => {
    setTouchEnd(e.targetTouches[0].clientX)
  }

  const onTouchEnd = () => {
    if (!touchStart || !touchEnd) return
    const distance = touchStart - touchEnd
    const isLeftSwipe = distance > minSwipeDistance
    const isRightSwipe = distance < -minSwipeDistance

    if (isLeftSwipe) {
      goToNext()
    } else if (isRightSwipe) {
      goToPrevious()
    }
  }

  if (loading) {
    return (
      <div className="loading">
        <div className="spinner"></div>
        <p>加载中...</p>
      </div>
    )
  }

  if (currentView === 'home') {
    return (
      <div className="home">
        <div className="home-content">
          <h1>英语单词图片浏览器</h1>
          <p>选择难度开始学习</p>
          <div className="difficulty-buttons">
            <button
              className="difficulty-btn easy-btn"
              onClick={() => loadImages('easy')}
            >
              <span className="btn-icon">📚</span>
              <span className="btn-text">简单单词</span>
              <span className="btn-desc">基础词汇学习</span>
            </button>
            <button
              className="difficulty-btn hard-btn"
              onClick={() => loadImages('hard')}
            >
              <span className="btn-icon">🎓</span>
              <span className="btn-text">困难单词</span>
              <span className="btn-desc">进阶词汇挑战</span>
            </button>
          </div>
        </div>
      </div>
    )
  }

  if (images.length === 0) {
    return (
      <div className="error">
        <p>没有找到图片</p>
        <button onClick={goHome}>返回首页</button>
      </div>
    )
  }

  const currentImage = images[currentImageIndex]
  const progress = ((currentImageIndex + 1) / images.length) * 100

  return (
    <div
      className="viewer"
      onTouchStart={onTouchStart}
      onTouchMove={onTouchMove}
      onTouchEnd={onTouchEnd}
    >
      <div className="viewer-header">
        <button className="back-btn" onClick={goHome}>
          ← 返回
        </button>
        <div className="progress-info">
          <span className="difficulty-label">
            {currentView === 'easy' ? '简单' : '困难'}
          </span>
          <span className="page-info">
            {currentImageIndex + 1} / {images.length}
          </span>
        </div>
      </div>

      <div className="progress-bar">
        <div
          className="progress-fill"
          style={{ width: `${progress}%` }}
        ></div>
      </div>

      <div className="image-container">
        <img
          src={`/images/${currentView}/${currentImage}`}
          alt={`单词图片 ${currentImageIndex + 1}`}
          className="word-image"
          onError={(e) => {
            console.error('图片加载失败:', currentImage)
            e.target.style.display = 'none'
          }}
        />
      </div>

      <div className="navigation">
        <button
          className="nav-btn prev-btn"
          onClick={goToPrevious}
          disabled={currentImageIndex === 0}
        >
          ← 上一张
        </button>
        <button
          className="nav-btn next-btn"
          onClick={goToNext}
          disabled={currentImageIndex === images.length - 1}
        >
          下一张 →
        </button>
      </div>

      <div className="help-text">
        <p>💡 提示：可以使用键盘方向键或滑动手势翻页</p>
      </div>
    </div>
  )
}

export default App
