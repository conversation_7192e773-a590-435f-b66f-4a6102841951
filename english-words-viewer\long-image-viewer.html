<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>英语单词长图浏览器 v2.0</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            background: #000; color: white; overflow: hidden;
        }
        
        /* 首页样式 */
        .home { 
            display: flex; justify-content: center; align-items: center;
            min-height: 100vh; padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .home-content { text-align: center; color: white; max-width: 500px; width: 100%; }
        .home h1 { 
            font-size: 2.5rem; margin-bottom: 20px; font-weight: 700;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        .home p { font-size: 1.2rem; margin-bottom: 40px; opacity: 0.9; }
        .difficulty-buttons { display: flex; flex-direction: column; gap: 20px; align-items: center; }
        .btn { 
            display: flex; flex-direction: column; align-items: center;
            padding: 25px 40px; border: none; border-radius: 15px;
            background: rgba(255, 255, 255, 0.95); color: #333;
            cursor: pointer; transition: all 0.3s ease;
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            width: 100%; max-width: 300px; min-height: 100px;
        }
        .btn:hover { transform: translateY(-5px); box-shadow: 0 15px 35px rgba(0,0,0,0.2); }
        .btn-icon { font-size: 2rem; margin-bottom: 8px; }
        .btn-text { font-size: 1.3rem; font-weight: 600; margin-bottom: 5px; }
        .btn-desc { font-size: 0.9rem; opacity: 0.7; }
        
        /* 长图浏览器样式 */
        .viewer { 
            display: none; position: fixed; top: 0; left: 0; 
            width: 100vw; height: 100vh; background: #000;
            z-index: 1000;
        }
        .viewer.active { display: flex; flex-direction: column; }
        
        /* 顶部控制栏 */
        .top-bar { 
            position: fixed; top: 0; left: 0; right: 0; 
            background: rgba(0,0,0,0.8); backdrop-filter: blur(10px);
            padding: 15px 20px; z-index: 1001;
            display: flex; justify-content: space-between; align-items: center;
            transition: opacity 0.3s ease;
        }
        .top-bar.hidden { opacity: 0; pointer-events: none; }
        
        .back-btn { 
            background: #667eea; color: white; padding: 8px 16px;
            border: none; border-radius: 20px; cursor: pointer; font-size: 0.9rem;
        }
        .progress-info { text-align: right; color: white; }
        .difficulty-label { font-size: 0.8rem; opacity: 0.8; margin-bottom: 2px; }
        .page-info { font-size: 1rem; font-weight: 600; }
        
        /* 图片容器 */
        .image-container { 
            flex: 1; width: 100%; height: 100vh;
            overflow-y: auto; overflow-x: hidden;
            -webkit-overflow-scrolling: touch;
            scrollbar-width: none; /* Firefox */
        }
        .image-container::-webkit-scrollbar { display: none; } /* Chrome/Safari */
        
        .word-image {
            width: 70%; height: auto; display: block;
            max-width: none; max-height: none;
            margin: 0 auto; /* 居中显示 */
        }
        
        /* 底部控制栏 */
        .bottom-bar { 
            position: fixed; bottom: 0; left: 0; right: 0;
            background: rgba(0,0,0,0.8); backdrop-filter: blur(10px);
            padding: 15px 20px; z-index: 1001;
            display: flex; justify-content: space-between; align-items: center;
            transition: opacity 0.3s ease;
        }
        .bottom-bar.hidden { opacity: 0; pointer-events: none; }
        
        .nav-btn { 
            background: rgba(255,255,255,0.2); color: white; 
            border: none; border-radius: 20px; padding: 10px 20px;
            cursor: pointer; font-size: 0.9rem; font-weight: 600;
            transition: all 0.3s ease; min-width: 80px;
        }
        .nav-btn:hover { background: rgba(255,255,255,0.3); }
        .nav-btn:disabled { opacity: 0.3; cursor: not-allowed; }
        
        .zoom-controls { display: flex; gap: 10px; align-items: center; }
        .zoom-btn { 
            background: rgba(255,255,255,0.2); color: white;
            border: none; border-radius: 50%; width: 40px; height: 40px;
            cursor: pointer; font-size: 1.2rem; display: flex;
            align-items: center; justify-content: center;
        }
        .zoom-btn:hover { background: rgba(255,255,255,0.3); }
        
        /* 状态提示 */
        .status { 
            position: fixed; top: 50%; left: 50%; 
            transform: translate(-50%, -50%);
            background: rgba(0,0,0,0.8); color: white;
            padding: 20px 30px; border-radius: 10px;
            font-size: 1rem; text-align: center; z-index: 1002;
        }
        .loading { color: #667eea; }
        .error { color: #ff6b6b; }
        
        /* 操作提示 */
        .tip { 
            position: fixed; top: 50%; left: 50%; 
            transform: translate(-50%, -50%);
            background: rgba(0,0,0,0.9); color: white;
            padding: 20px 30px; border-radius: 15px;
            font-size: 0.9rem; text-align: center; z-index: 1002;
            max-width: 80%; opacity: 0; transition: opacity 0.3s ease;
        }
        .tip.show { opacity: 1; }
        
        /* 手机端优化 */
        @media (max-width: 768px) {
            .home h1 { font-size: 2rem; }
            .btn { padding: 20px 30px; min-height: 90px; }
            .btn-icon { font-size: 1.8rem; }
            .btn-text { font-size: 1.1rem; }
            .top-bar, .bottom-bar { padding: 10px 15px; }
            .back-btn { padding: 6px 12px; font-size: 0.8rem; }
            .nav-btn { padding: 8px 16px; font-size: 0.8rem; min-width: 70px; }
            .zoom-btn { width: 35px; height: 35px; font-size: 1rem; }
        }
    </style>
</head>
<body>
    <div id="home" class="home">
        <div class="home-content">
            <h1>📚 英语单词长图浏览器</h1>
            <p>专为长图设计的浏览器 v2.0 - 已更新自定义命名</p>
            <div class="difficulty-buttons">
                <button class="btn" onclick="loadImages('easy')">
                    <span class="btn-icon">📖</span>
                    <span class="btn-text">简单单词</span>
                    <span class="btn-desc">基础词汇学习 (115张长图)</span>
                </button>
                <button class="btn" onclick="loadImages('hard')">
                    <span class="btn-icon">🎓</span>
                    <span class="btn-text">困难单词</span>
                    <span class="btn-desc">进阶词汇挑战 (89张长图)</span>
                </button>
            </div>
        </div>
    </div>

    <div id="viewer" class="viewer">
        <div id="top-bar" class="top-bar">
            <button class="back-btn" onclick="goHome()">← 返回</button>
            <div class="progress-info">
                <div class="difficulty-label" id="difficulty-label">简单</div>
                <div class="page-info" id="page-info">1 / 1</div>
            </div>
        </div>
        
        <div class="image-container" id="image-container">
            <img id="word-image" src="" alt="单词图片" class="word-image" style="display: none;">
        </div>
        
        <div id="bottom-bar" class="bottom-bar">
            <button class="nav-btn" id="prev-btn" onclick="goToPrevious()">← 上一张</button>
            <div class="zoom-controls">
                <button class="zoom-btn" onclick="resetZoom()" title="重置">⌂</button>
                <button class="zoom-btn" onclick="toggleFullscreen()" title="全屏">⛶</button>
            </div>
            <button class="nav-btn" id="next-btn" onclick="goToNext()">下一张 →</button>
        </div>
        
        <div id="status" class="status" style="display: none;">正在加载图片...</div>
        <div id="tip" class="tip">
            📱 长图浏览提示：<br>
            • 上下滑动查看完整内容<br>
            • 左右滑动切换图片<br>
            • 点击屏幕隐藏/显示控制栏<br>
            • 双击图片重置位置
        </div>
    </div>

    <script>
        let currentView = 'home';
        let currentImageIndex = 0;
        let images = [];
        let controlsVisible = true;
        let hideControlsTimer = null;

        // 根据你的自定义命名更新的图片列表
        const easyImages = [
            "01.png", "02.png", "03.png", "04.png", "05.png", "06.png", "07.png", "08.png", "09.png",
            "010.png", "011.png", "012.png", "013.png", "014.png", "015.png", "016.png", "017.png", "018.png", "019.png",
            "020.png", "021.png", "022.png", "023.png", "024.png", "025.png", "026.png", "027.png", "028.png", "029.png",
            "030.png", "031.png", "032.png", "033.png", "034.png", "035.png", "036.png", "037.png", "038.png", "039.png",
            "040.png", "041.png", "042.png", "043.png", "044.png", "045.png", "046.png", "047.PNG", "048.PNG", "049.PNG",
            "050.PNG", "051.PNG", "052.PNG", "053.PNG", "054.PNG", "055.PNG", "056.PNG", "057.PNG", "058.png", "059.png",
            "060.png", "061.png", "062.png", "063.png", "064.png", "065.png", "066.png", "067.png", "068.png", "069.png",
            "070.png", "071.png", "072.png", "073.png", "074.png", "075.png", "076.png", "077.png", "078.png", "079.png",
            "080.png", "081.png", "082.png", "083.png", "084.png", "085.png", "086.png", "087.png", "088.png", "089.png",
            "090.png", "091.png", "092.png", "093.png", "094.png", "095.png", "096.png", "097.png", "098.png", "099.png",
            "0100.png", "0101.png", "0102.png", "0103.png", "0104.png", "0105.png", "0106.png", "0107.png", "0108.png", "0109.png",
            "0110.png", "0111.png", "0112.png", "0113.png", "0114.png"
        ];

        const hardImages = [
            "01.PNG", "02.PNG", "03.PNG", "04.PNG", "05.PNG", "06.PNG", "07.PNG", "08.PNG", "09.PNG",
            "010.PNG", "011.PNG", "012.PNG", "013.PNG", "014.PNG", "015.PNG", "016.PNG", "017.PNG", "018.PNG", "019.PNG",
            "020.PNG", "021.PNG", "022.PNG", "023.PNG", "024.PNG", "025.PNG", "026.PNG", "027.PNG", "028.PNG", "029.PNG",
            "030.PNG", "031.PNG", "032.PNG", "033.PNG", "034.PNG", "035.PNG", "036.PNG", "037.PNG", "038.PNG", "039.PNG",
            "040.PNG", "041.PNG", "042.PNG", "043.PNG", "044.PNG", "045.PNG", "046.PNG", "047.PNG", "048.PNG", "049.PNG",
            "050.PNG", "051.PNG", "052.PNG", "053.PNG", "054.PNG", "055.PNG", "056.PNG", "057.PNG", "058.PNG", "059.PNG",
            "060.PNG", "061.PNG", "062.PNG", "063.PNG", "064.PNG", "065.PNG", "066.PNG", "067.PNG", "068.PNG", "069.PNG",
            "070.PNG", "071.PNG", "072.PNG", "073.PNG", "074.PNG", "075.PNG", "076.PNG", "077.PNG", "078.PNG", "079.PNG",
            "080.PNG", "081.PNG", "082.PNG", "083.PNG", "084.PNG", "085.PNG", "086.PNG", "087.PNG", "088.PNG"
        ];

        function loadImages(difficulty) {
            try {
                images = difficulty === 'easy' ? easyImages : hardImages;
                currentImageIndex = 0;
                currentView = difficulty;
                
                document.getElementById('home').style.display = 'none';
                document.getElementById('viewer').classList.add('active');
                
                updateViewer();
                showTip();
            } catch (error) {
                console.error('加载图片失败:', error);
                alert('加载图片失败，请刷新页面重试');
            }
        }

        function updateViewer() {
            const currentImage = images[currentImageIndex];

            // 调试信息
            console.log('当前图片:', currentImage);
            console.log('图片路径:', `public/images/${currentView}/${currentImage}`);

            // 更新进度信息
            document.getElementById('difficulty-label').textContent = currentView === 'easy' ? '简单' : '困难';
            document.getElementById('page-info').textContent = `${currentImageIndex + 1} / ${images.length}`;
            
            // 更新图片
            const imgElement = document.getElementById('word-image');
            const statusElement = document.getElementById('status');
            
            // 显示加载状态
            imgElement.style.display = 'none';
            statusElement.style.display = 'block';
            statusElement.className = 'status loading';
            statusElement.textContent = '正在加载图片...';
            
            // 设置图片加载事件
            imgElement.onload = function() {
                statusElement.style.display = 'none';
                imgElement.style.display = 'block';
                resetZoom();
            };
            
            imgElement.onerror = function() {
                console.error('图片加载失败:', currentImage);
                statusElement.className = 'status error';
                statusElement.textContent = `图片加载失败: ${currentImage}`;
            };
            
            // 设置图片路径 - 使用你的自定义路径
            imgElement.src = `public/images/${currentView}/${currentImage}`;
            
            // 更新导航按钮
            document.getElementById('prev-btn').disabled = currentImageIndex === 0;
            document.getElementById('next-btn').disabled = currentImageIndex === images.length - 1;
        }

        function goToNext() {
            if (currentImageIndex < images.length - 1) {
                currentImageIndex++;
                updateViewer();
            }
        }

        function goToPrevious() {
            if (currentImageIndex > 0) {
                currentImageIndex--;
                updateViewer();
            }
        }

        function goHome() {
            currentView = 'home';
            images = [];
            currentImageIndex = 0;
            
            document.getElementById('home').style.display = 'flex';
            document.getElementById('viewer').classList.remove('active');
        }

        function resetZoom() {
            const container = document.getElementById('image-container');
            container.scrollTop = 0;
        }

        function toggleFullscreen() {
            if (!document.fullscreenElement) {
                document.documentElement.requestFullscreen();
            } else {
                document.exitFullscreen();
            }
        }

        function showTip() {
            const tip = document.getElementById('tip');
            tip.classList.add('show');
            setTimeout(() => {
                tip.classList.remove('show');
            }, 5000);
        }

        function toggleControls() {
            const topBar = document.getElementById('top-bar');
            const bottomBar = document.getElementById('bottom-bar');
            
            controlsVisible = !controlsVisible;
            
            if (controlsVisible) {
                topBar.classList.remove('hidden');
                bottomBar.classList.remove('hidden');
            } else {
                topBar.classList.add('hidden');
                bottomBar.classList.add('hidden');
            }
        }

        function autoHideControls() {
            if (hideControlsTimer) {
                clearTimeout(hideControlsTimer);
            }
            
            hideControlsTimer = setTimeout(() => {
                if (controlsVisible) {
                    toggleControls();
                }
            }, 3000);
        }

        // 键盘导航
        document.addEventListener('keydown', function(e) {
            if (currentView === 'easy' || currentView === 'hard') {
                if (e.key === 'ArrowLeft') {
                    goToPrevious();
                } else if (e.key === 'ArrowRight') {
                    goToNext();
                } else if (e.key === 'Escape') {
                    goHome();
                } else if (e.key === ' ') {
                    e.preventDefault();
                    toggleControls();
                }
            }
        });

        // 触摸手势支持
        let touchStartX = null;
        let touchStartY = null;
        let isScrolling = false;

        document.addEventListener('touchstart', function(e) {
            touchStartX = e.touches[0].clientX;
            touchStartY = e.touches[0].clientY;
            isScrolling = false;
        });

        document.addEventListener('touchmove', function(e) {
            if (touchStartX === null || touchStartY === null) return;
            
            const touchCurrentX = e.touches[0].clientX;
            const touchCurrentY = e.touches[0].clientY;
            const diffX = Math.abs(touchStartX - touchCurrentX);
            const diffY = Math.abs(touchStartY - touchCurrentY);
            
            if (diffY > diffX) {
                isScrolling = true;
            }
        });

        document.addEventListener('touchend', function(e) {
            if (touchStartX === null || touchStartY === null || isScrolling) {
                touchStartX = null;
                touchStartY = null;
                isScrolling = false;
                return;
            }
            
            const touchEndX = e.changedTouches[0].clientX;
            const diffX = touchStartX - touchEndX;
            
            if (Math.abs(diffX) > 80) {
                if (diffX > 0) {
                    goToNext();
                } else {
                    goToPrevious();
                }
            }
            
            touchStartX = null;
            touchStartY = null;
            isScrolling = false;
        });

        // 点击切换控制栏
        document.getElementById('image-container').addEventListener('click', function(e) {
            if (e.target.tagName === 'IMG') {
                toggleControls();
            }
        });

        // 双击重置位置
        document.getElementById('word-image').addEventListener('dblclick', function() {
            resetZoom();
        });

        // 鼠标移动时显示控制栏
        document.addEventListener('mousemove', function() {
            if (!controlsVisible) {
                toggleControls();
            }
            autoHideControls();
        });
    </script>
</body>
</html>
