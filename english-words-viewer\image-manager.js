#!/usr/bin/env node

/**
 * 图片管理工具
 * 提供图片文件的管理功能：查看、统计、验证等
 */

import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

// 支持的图片格式
const imageExtensions = ['.png', '.PNG', '.jpg', '.JPG', '.jpeg', '.JPEG', '.gif', '.GIF', '.webp', '.WEBP']

// 检查文件是否为图片
function isImageFile(filename) {
  const ext = path.extname(filename)
  return imageExtensions.includes(ext)
}

// 自然排序函数
function naturalSort(a, b) {
  return a.localeCompare(b, undefined, { numeric: true, sensitivity: 'base' })
}

// 获取文件大小（人类可读格式）
function getFileSize(filePath) {
  try {
    const stats = fs.statSync(filePath)
    const bytes = stats.size
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  } catch (error) {
    return 'Unknown'
  }
}

// 扫描并分析图片文件夹
function analyzeImagesFolder(difficulty) {
  const folderPath = path.join(__dirname, 'public', 'images', difficulty)
  const jsonPath = path.join(__dirname, 'public', `${difficulty}-images.json`)
  
  console.log(`\n📁 分析 ${difficulty.toUpperCase()} 文件夹`)
  console.log(`   路径: ${folderPath}`)
  
  if (!fs.existsSync(folderPath)) {
    console.log(`   ❌ 文件夹不存在`)
    return { files: [], totalSize: 0, errors: ['文件夹不存在'] }
  }

  const files = fs.readdirSync(folderPath)
  const imageFiles = files.filter(isImageFile).sort(naturalSort)
  const nonImageFiles = files.filter(f => !isImageFile(f))
  
  let totalSize = 0
  const fileDetails = []
  const errors = []

  // 分析每个图片文件
  imageFiles.forEach((filename, index) => {
    const filePath = path.join(folderPath, filename)
    const size = getFileSize(filePath)
    const stats = fs.statSync(filePath)
    totalSize += stats.size
    
    fileDetails.push({
      index: index + 1,
      filename,
      size,
      bytes: stats.size,
      modified: stats.mtime.toISOString().split('T')[0]
    })
  })

  // 检查JSON文件
  let jsonExists = false
  let jsonFiles = []
  if (fs.existsSync(jsonPath)) {
    jsonExists = true
    try {
      const jsonContent = fs.readFileSync(jsonPath, 'utf8')
      jsonFiles = JSON.parse(jsonContent)
    } catch (error) {
      errors.push(`JSON文件解析失败: ${error.message}`)
    }
  }

  // 输出统计信息
  console.log(`   📊 统计信息:`)
  console.log(`      图片文件: ${imageFiles.length} 个`)
  console.log(`      总大小: ${(totalSize / 1024 / 1024).toFixed(2)} MB (${totalSize} bytes)`)
  console.log(`      JSON文件: ${jsonExists ? '✅ 存在' : '❌ 不存在'}`)
  
  if (jsonExists && jsonFiles.length !== imageFiles.length) {
    console.log(`      ⚠️  JSON文件与实际文件数量不匹配 (JSON: ${jsonFiles.length}, 实际: ${imageFiles.length})`)
    errors.push('JSON文件与实际文件数量不匹配')
  }

  if (nonImageFiles.length > 0) {
    console.log(`      ⚠️  非图片文件: ${nonImageFiles.length} 个`)
    console.log(`         ${nonImageFiles.join(', ')}`)
  }

  // 显示文件列表（前10个和后5个）
  if (imageFiles.length > 0) {
    console.log(`   📋 文件列表:`)
    const showCount = Math.min(10, imageFiles.length)
    fileDetails.slice(0, showCount).forEach(file => {
      console.log(`      ${file.index.toString().padStart(3, ' ')}. ${file.filename} (${file.size})`)
    })
    
    if (imageFiles.length > 15) {
      console.log(`      ... (省略 ${imageFiles.length - 15} 个文件)`)
      fileDetails.slice(-5).forEach(file => {
        console.log(`      ${file.index.toString().padStart(3, ' ')}. ${file.filename} (${file.size})`)
      })
    } else if (imageFiles.length > 10) {
      fileDetails.slice(10).forEach(file => {
        console.log(`      ${file.index.toString().padStart(3, ' ')}. ${file.filename} (${file.size})`)
      })
    }
  }

  return {
    files: imageFiles,
    fileDetails,
    totalSize,
    totalSizeFormatted: (totalSize / 1024 / 1024).toFixed(2) + ' MB',
    jsonExists,
    jsonFiles,
    nonImageFiles,
    errors
  }
}

// 验证图片完整性
function validateImages() {
  console.log('🔍 验证图片完整性...\n')
  
  const easyResult = analyzeImagesFolder('easy')
  const hardResult = analyzeImagesFolder('hard')
  
  console.log('\n📈 总体统计:')
  console.log(`   简单图片: ${easyResult.files.length} 个`)
  console.log(`   困难图片: ${hardResult.files.length} 个`)
  console.log(`   总计: ${easyResult.files.length + hardResult.files.length} 个图片`)
  
  const totalErrors = [...easyResult.errors, ...hardResult.errors]
  if (totalErrors.length > 0) {
    console.log('\n⚠️  发现问题:')
    totalErrors.forEach((error, index) => {
      console.log(`   ${index + 1}. ${error}`)
    })
    console.log('\n💡 建议运行: npm run generate-images')
  } else {
    console.log('\n✅ 所有图片文件验证通过！')
  }
}

// 主函数
function main() {
  const args = process.argv.slice(2)
  const command = args[0] || 'validate'
  
  console.log('🖼️  图片管理工具\n')
  
  switch (command) {
    case 'validate':
    case 'check':
      validateImages()
      break
    case 'easy':
      analyzeImagesFolder('easy')
      break
    case 'hard':
      analyzeImagesFolder('hard')
      break
    case 'help':
      console.log('使用方法:')
      console.log('  node image-manager.js [command]')
      console.log('')
      console.log('命令:')
      console.log('  validate, check  验证所有图片 (默认)')
      console.log('  easy            只分析简单图片')
      console.log('  hard            只分析困难图片')
      console.log('  help            显示帮助信息')
      break
    default:
      console.log(`未知命令: ${command}`)
      console.log('运行 node image-manager.js help 查看帮助')
  }
}

// 如果直接运行此脚本
if (process.argv[1] && process.argv[1].endsWith('image-manager.js')) {
  main()
}

export { analyzeImagesFolder, validateImages }
