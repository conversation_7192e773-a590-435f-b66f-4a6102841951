# 英语单词图片浏览器

一个现代化的英语单词学习图片浏览器，专为长图设计，支持分难度浏览。

## 🌟 特性

- **双难度模式**：简单单词 vs 困难单词
- **长图优化**：专为390x16371像素长图设计
- **翻页浏览**：一张图片一页，支持上一张/下一张
- **响应式设计**：完美适配手机和电脑
- **多种操作方式**：
  - 点击按钮
  - 键盘方向键（←→切换图片）
  - 手机滑动手势（左右切换）
  - ESC键返回首页
- **进度显示**：实时显示当前页码和总页数
- **现代化界面**：使用React构建，界面美观流畅

## 📊 图片统计

- **简单单词**：115张图片
- **困难单词**：89张图片
- **总计**：204张图片
- **图片规格**：390 x 16371 像素（超长垂直图片）
- **显示宽度**：屏幕宽度的70%（优化阅读体验）

## 🚀 快速开始

### 在线访问

访问部署在Vercel上的在线版本：[English Words Viewer](https://your-vercel-url.vercel.app)

### 本地开发

1. 克隆仓库
```bash
git clone https://github.com/your-username/english-words-viewer.git
cd english-words-viewer
```

2. 安装依赖
```bash
npm install
```

3. 启动开发服务器
```bash
npm run dev
```

4. 在浏览器中访问 `http://localhost:5173`

### 构建生产版本

```bash
npm run build
```

## 📱 操作说明

### 桌面端
- 使用键盘左右方向键切换图片
- 点击"上一张"/"下一张"按钮
- 按ESC键返回首页
- 鼠标滚轮滚动查看长图内容

### 手机端
- 左右滑动切换图片
- 上下滑动查看长图内容
- 支持触摸手势操作

## 🛠️ 技术栈

- **前端框架**：React 19
- **构建工具**：Vite
- **样式**：CSS3 with Flexbox & Grid
- **部署**：Vercel
- **代码规范**：ESLint

## 📁 项目结构

```
english-words-viewer/
├── public/
│   ├── images/
│   │   ├── easy/          # 简单单词图片
│   │   └── hard/          # 困难单词图片
│   ├── easy-images.json   # 简单单词图片列表
│   └── hard-images.json   # 困难单词图片列表
├── src/
│   ├── App.jsx           # 主应用组件
│   ├── App.css           # 主样式文件
│   ├── main.jsx          # 应用入口
│   └── index.css         # 全局样式
├── index.html            # HTML模板
├── package.json          # 项目配置
├── vite.config.js        # Vite配置
└── vercel.json           # Vercel部署配置
```

## 🎨 自定义

### 添加更多图片

1. 将图片放入对应的文件夹（`public/images/easy/` 或 `public/images/hard/`）
2. 在对应的JSON文件中添加文件名（`public/easy-images.json` 或 `public/hard-images.json`）
3. 重新构建项目

### 修改样式

主要样式文件位于 `src/App.css`，可以根据需要调整：
- 图片显示宽度（当前为70%）
- 颜色主题
- 布局样式

## 📄 许可证

本项目仅供学习使用。

## 🤝 贡献

欢迎提交Issue和Pull Request！

## 📞 联系

如有问题，请通过GitHub Issues联系。
