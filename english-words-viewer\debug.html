<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>调试页面</title>
</head>
<body>
    <h1>图片路径调试</h1>
    <div id="debug-info"></div>
    
    <script>
        // 困难单词图片列表
        const hardImages = [
            "01.PNG", "02.PNG", "03.PNG", "04.PNG", "05.PNG", "06.PNG", "07.PNG", "08.PNG", "09.PNG",
            "010.PNG", "011.PNG", "012.PNG", "013.PNG", "014.PNG", "015.PNG", "016.PNG", "017.PNG", "018.PNG", "019.PNG",
            "020.PNG", "021.PNG", "022.PNG", "023.PNG", "024.PNG", "025.PNG", "026.PNG", "027.PNG", "028.PNG", "029.PNG",
            "030.PNG", "031.PNG", "032.PNG", "033.PNG", "034.PNG", "035.PNG", "036.PNG", "037.PNG", "038.PNG", "039.PNG",
            "040.PNG", "041.PNG", "042.PNG", "043.PNG", "044.PNG", "045.PNG", "046.PNG", "047.PNG", "048.PNG", "049.PNG",
            "050.PNG", "051.PNG", "052.PNG", "053.PNG", "054.PNG", "055.PNG", "056.PNG", "057.PNG", "058.PNG", "059.PNG",
            "060.PNG", "061.PNG", "062.PNG", "063.PNG", "064.PNG", "065.PNG", "066.PNG", "067.PNG", "068.PNG", "069.PNG",
            "070.PNG", "071.PNG", "072.PNG", "073.PNG", "074.PNG", "075.PNG", "076.PNG", "077.PNG", "078.PNG", "079.PNG",
            "080.PNG", "081.PNG", "082.PNG", "083.PNG", "084.PNG", "085.PNG", "086.PNG", "087.PNG", "088.PNG"
        ];
        
        // 显示调试信息
        const debugDiv = document.getElementById('debug-info');
        debugDiv.innerHTML = `
            <h2>困难单词图片列表（前10张）：</h2>
            <ul>
                ${hardImages.slice(0, 10).map((img, index) => 
                    `<li>${index + 1}: ${img}</li>`
                ).join('')}
            </ul>
            
            <h2>完整路径测试：</h2>
            <p>第一张图片路径: public/images/hard/${hardImages[0]}</p>
            
            <h2>图片测试：</h2>
            <img src="public/images/hard/${hardImages[0]}" alt="测试图片" style="max-width: 200px;" 
                 onload="console.log('图片加载成功: ${hardImages[0]}')" 
                 onerror="console.log('图片加载失败: ${hardImages[0]}')">
        `;
    </script>
</body>
</html>
