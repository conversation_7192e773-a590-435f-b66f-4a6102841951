英语单词长图浏览器 - 使用说明
=====================================

🚀 快速开始
-----------

方法一：使用React应用（推荐）
1. 安装Node.js（如果尚未安装）
2. 在项目文件夹中打开命令行
3. 运行 npm install 安装依赖
4. 运行 npm run dev 启动开发服务器
5. 在浏览器中访问显示的地址（通常是 http://localhost:5173）

方法二：使用HTTP服务器
1. 双击 start.bat 启动服务器
2. 在浏览器中访问 http://localhost:9000/index.html

🎯 功能特点
-----------
- 双难度模式：简单单词 vs 困难单词
- 长图专用：专为390x16371像素长图优化
- 翻页浏览：一张图片一页，支持上一张/下一张
- 长图滚动：上下滚动查看完整长图内容
- 多种操作方式：
  * 点击按钮
  * 键盘方向键（←→切换图片，↑↓滚动图片）
  * 手机滑动手势（左右切换，上下滚动）
  * ESC键返回首页
  * 点击图片隐藏/显示控制栏
  * 双击图片重置到顶部
- 进度显示：实时显示当前页码和总页数
- 全屏模式：支持全屏浏览
- 响应式设计：完美适配手机和电脑
- 沉浸式体验：黑色背景，专注阅读

📱 操作说明
-----------

桌面端：
- 使用键盘左右方向键切换图片
- 使用键盘上下方向键滚动长图
- 点击"上一张"/"下一张"按钮
- 按ESC键返回首页
- 按空格键隐藏/显示控制栏
- 双击图片重置到顶部
- 点击全屏按钮进入全屏模式

手机端：
- 左右滑动切换图片
- 上下滑动查看长图内容
- 点击图片隐藏/显示控制栏
- 双击图片重置到顶部
- 支持触摸手势操作

🔧 故障排除
-----------

图片无法显示：
1. 确保图片文件在 public/images/easy/ 和 public/images/hard/ 文件夹中
2. 检查图片文件名是否与代码中的列表匹配
3. 尝试使用HTTP服务器而不是直接打开HTML文件

服务器无法启动：
1. 确保已安装Python或Node.js
2. 尝试手动运行：python -m http.server 9000
3. 或者直接双击 viewer.html 文件

📊 图片统计
-----------
- 简单单词：115张图片（01.png ~ 0114.png）
- 困难单词：89张图片（01.PNG ~ 088.PNG）
- 总计：204张图片

🎨 自定义
---------
如需添加更多图片：
1. 将图片放入对应的文件夹（public/images/easy/ 或 public/images/hard/）
2. 在对应的JSON文件中添加文件名（public/easy-images.json 或 public/hard-images.json）
3. 刷新页面即可

💡 重要文件
-----------
- index.html - React应用入口文件
- src/App.jsx - 主要应用组件
- src/App.css - 样式文件
- start.bat - Windows启动脚本
- public/images/easy/ - 简单单词图片文件夹（390x16371像素长图）
- public/images/hard/ - 困难单词图片文件夹（390x16371像素长图）
- public/easy-images.json - 简单单词图片列表
- public/hard-images.json - 困难单词图片列表

🔍 图片规格
-----------
- 图片尺寸：390 x 16371 像素（超长垂直图片）
- 图片格式：PNG
- 图片内容：英语单词学习材料
- 显示宽度：屏幕宽度的70%（优化后，更易阅读）
- 适合：手机竖屏浏览，需要滚动查看完整内容

✅ 图片已就绪
-----------
你已经完成了图片的自定义重命名！
当前图片位置：
- 简单单词：public/images/easy/ (01.png ~ 0114.png)
- 困难单词：public/images/hard/ (01.PNG ~ 088.PNG)

🎯 你的自定义命名规则
-------------------
- 简单单词：混合格式（01.png, 010.png, 0100.png等）
- 困难单词：统一格式（01.PNG ~ 088.PNG）
- 文件扩展名：混合使用.png和.PNG
- 代码已更新匹配你的命名方案

📄 许可证
---------
本项目仅供学习使用。
