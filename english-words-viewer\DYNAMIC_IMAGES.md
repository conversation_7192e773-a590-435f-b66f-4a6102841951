# 动态图片管理功能说明

## 🎯 功能概述

现在你的英语单词图片浏览器支持**伪静态动态图片发现**功能！这意味着：

✅ **添加图片**：直接将图片文件放入文件夹，应用自动识别  
✅ **删除图片**：删除文件夹中的图片，应用自动更新  
✅ **重命名图片**：修改图片文件名，应用自动适应  
✅ **无需手动编辑**：不需要修改JSON文件或代码  

## 🔄 工作原理

### 双重加载机制
1. **优先使用JSON文件**：如果存在 `easy-images.json` 或 `hard-images.json`，优先使用
2. **自动回退到动态发现**：如果JSON文件不存在或为空，自动扫描文件夹

### 动态发现算法
- 批量并发检测文件存在性（每批10个文件）
- 支持多种命名模式：
  - `01.png`, `02.png` ... `99.png`
  - `010.png`, `011.png` ... `0199.png`  
  - `0100.png`, `0101.png` ... `0199.png`
  - 支持 `.png` 和 `.PNG` 扩展名

## 📁 文件夹结构

```
public/images/
├── easy/           # 简单单词图片
│   ├── 01.png
│   ├── 02.png
│   └── ...
└── hard/           # 困难单词图片
    ├── 01.PNG
    ├── 02.PNG
    └── ...
```

## 🛠️ 管理命令

### 生成图片列表
```bash
npm run generate-images
```
- 扫描 `public/images/easy/` 和 `public/images/hard/` 文件夹
- 生成 `easy-images.json` 和 `hard-images.json` 文件
- 自动按文件名排序

### 检查图片状态
```bash
npm run check-images
```
- 验证图片文件完整性
- 显示详细统计信息
- 检查JSON文件与实际文件的一致性

### 分析特定难度
```bash
node image-manager.js easy    # 只分析简单图片
node image-manager.js hard    # 只分析困难图片
```

## 📝 使用场景

### 场景1：添加新图片
1. 将新图片文件复制到对应文件夹
2. 运行 `npm run generate-images` 更新列表
3. 刷新浏览器即可看到新图片

### 场景2：删除图片
1. 从文件夹中删除不需要的图片
2. 运行 `npm run generate-images` 更新列表
3. 刷新浏览器，图片自动从列表中移除

### 场景3：重命名图片
1. 修改图片文件名
2. 运行 `npm run generate-images` 重新生成列表
3. 新的文件名会自动按顺序排列

### 场景4：完全动态模式
1. 删除 `easy-images.json` 和 `hard-images.json` 文件
2. 应用会自动切换到动态发现模式
3. 每次加载时自动扫描文件夹（较慢但完全动态）

## ⚡ 性能优化

### JSON模式（推荐）
- **优点**：加载速度快，适合生产环境
- **缺点**：需要手动更新JSON文件
- **适用**：图片相对固定的场景

### 动态发现模式
- **优点**：完全自动，无需维护JSON文件
- **缺点**：首次加载较慢（需要检测文件存在性）
- **适用**：开发环境或图片经常变动的场景

## 🔧 自定义配置

### 修改支持的图片格式
编辑 `generate-image-lists.js` 中的 `imageExtensions` 数组：
```javascript
const imageExtensions = ['.png', '.PNG', '.jpg', '.JPG', '.jpeg', '.JPEG', '.gif', '.GIF', '.webp', '.WEBP']
```

### 修改命名模式检测
编辑 `src/App.jsx` 中的 `generatePossibleFilenames` 函数，添加新的命名模式。

## 🚀 部署注意事项

### Vercel部署
- 构建时会自动运行 `npm run generate-images`
- JSON文件会包含在构建产物中
- 部署后为静态模式，不支持动态发现

### 本地开发
- 支持完整的动态发现功能
- 可以实时添加/删除图片进行测试

## 🎯 最佳实践

1. **开发阶段**：使用动态发现模式，方便调试
2. **生产部署**：使用JSON模式，确保性能
3. **定期维护**：运行 `npm run check-images` 检查文件状态
4. **批量操作**：使用 `npm run generate-images` 批量更新

## 🔍 故障排除

### 图片不显示
1. 检查文件路径是否正确
2. 确认文件扩展名大小写
3. 运行 `npm run check-images` 诊断问题

### 动态发现失败
1. 检查网络连接
2. 确认文件命名符合支持的模式
3. 查看浏览器控制台错误信息

### JSON文件不同步
1. 运行 `npm run generate-images` 重新生成
2. 检查文件权限
3. 确认文件夹路径正确

## 📊 统计信息

当前项目状态：
- **简单图片**：114张 (519MB)
- **困难图片**：88张 (227MB)
- **总计**：202张图片
- **支持格式**：PNG, JPG, JPEG, GIF, WebP
- **命名模式**：数字序列 + 扩展名

这个动态图片管理系统让你的应用真正做到了"伪静态"——对用户完全透明，但管理起来非常灵活！🎉
