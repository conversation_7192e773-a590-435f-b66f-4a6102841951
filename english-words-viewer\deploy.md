# 部署指南

## GitHub 上传步骤

1. **初始化Git仓库**
```bash
git init
git add .
git commit -m "Initial commit: English Words Viewer"
```

2. **创建GitHub仓库**
- 在GitHub上创建新仓库 `english-words-viewer`
- 不要初始化README、.gitignore或license（因为本地已有）

3. **连接远程仓库并推送**
```bash
git remote add origin https://github.com/your-username/english-words-viewer.git
git branch -M main
git push -u origin main
```

## Vercel 部署步骤

### 方法一：通过Vercel网站部署（推荐）

1. **访问Vercel**
   - 打开 [vercel.com](https://vercel.com)
   - 使用GitHub账号登录

2. **导入项目**
   - 点击 "New Project"
   - 选择你的GitHub仓库 `english-words-viewer`
   - 点击 "Import"

3. **配置项目**
   - **Project Name**: `english-words-viewer`
   - **Framework Preset**: Vite
   - **Root Directory**: `./` (默认)
   - **Build Command**: `npm run build` (自动检测)
   - **Output Directory**: `dist` (自动检测)
   - **Install Command**: `npm install` (自动检测)

4. **部署**
   - 点击 "Deploy"
   - 等待构建完成
   - 获得部署URL

### 方法二：通过Vercel CLI部署

1. **安装Vercel CLI**
```bash
npm i -g vercel
```

2. **登录Vercel**
```bash
vercel login
```

3. **部署项目**
```bash
vercel
```

4. **生产部署**
```bash
vercel --prod
```

## 环境变量配置

如果需要环境变量，在Vercel项目设置中添加：

- `NODE_ENV`: `production`
- 其他自定义环境变量...

## 自定义域名

1. 在Vercel项目设置中
2. 进入 "Domains" 选项卡
3. 添加你的自定义域名
4. 按照提示配置DNS

## 自动部署

- 每次推送到main分支时，Vercel会自动重新部署
- Pull Request会创建预览部署

## 性能优化建议

1. **图片优化**
   - 确保图片已经过压缩
   - 考虑使用WebP格式

2. **缓存策略**
   - 图片文件缓存1年
   - JSON文件缓存1小时

3. **CDN加速**
   - Vercel自动提供全球CDN
   - 图片会自动通过CDN分发

## 故障排除

### 构建失败
- 检查package.json中的依赖版本
- 确保所有文件路径正确
- 查看Vercel构建日志

### 图片无法显示
- 确保图片文件在public/images/目录中
- 检查JSON文件中的文件名是否正确
- 验证文件大小写是否匹配

### 路由问题
- 确保vercel.json中的rewrites配置正确
- SPA应用需要将所有路由重定向到index.html

## 监控和分析

1. **Vercel Analytics**
   - 在项目设置中启用Analytics
   - 查看访问统计和性能指标

2. **错误监控**
   - 可以集成Sentry等错误监控服务
   - 在生产环境中监控错误

## 更新部署

1. **代码更新**
```bash
git add .
git commit -m "Update: description of changes"
git push origin main
```

2. **自动部署**
   - Vercel会自动检测到推送并重新部署
   - 通常在1-2分钟内完成

## 回滚

如果需要回滚到之前的版本：
1. 在Vercel项目面板中
2. 进入 "Deployments" 选项卡
3. 找到要回滚的版本
4. 点击 "Promote to Production"
